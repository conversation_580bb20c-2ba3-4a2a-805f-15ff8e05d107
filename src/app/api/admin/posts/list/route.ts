import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

export async function GET(request: NextRequest) {
  try {
    // Get all posts (including drafts for admin view)
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      ORDER BY created_at DESC
    `;
    
    const result = await db.executeQuery(query);
    const posts = result.results || [];

    return NextResponse.json({
      success: true,
      posts: posts
    })
  } catch (error) {
    console.error('Error fetching posts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    )
  }
}
