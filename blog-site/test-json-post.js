// Test script for JSON Post Creator
// Run with: node test-json-post.js

const testJsonData = {
  "blogPost": {
    "metadata": {
      "title": "Test Post from JSON Creator",
      "author": "Test Author",
      "category": "Test Category",
      "tags": ["test", "json", "creator"],
      "publishDate": "2025-07-30",
      "status": "draft",
      "seoDescription": "This is a test post created using the JSON Post Creator",
      "readTime": "2 min read"
    },
    "content": {
      "introduction": {
        "text": "This is a test post created using the JSON Post Creator to verify functionality.",
        "image": {
          "url": "https://via.placeholder.com/600x300/4a90e2/ffffff?text=Test+Introduction",
          "alt": "Test introduction image",
          "caption": "Test introduction caption"
        }
      },
      "sections": [
        {
          "id": 1,
          "type": "quote_card",
          "title": "Test Quote Section",
          "content": {
            "quotes": [
              {
                "emoji": "🧪",
                "text": "Testing is the key to quality,"
              },
              {
                "emoji": "✅",
                "text": "Quality is the key to success,"
              },
              {
                "emoji": "🚀",
                "text": "Success is the key to growth."
              }
            ],
            "backgroundStyle": {
              "backgroundColor": "#f0f8ff",
              "borderColor": "#4a90e2",
              "textColor": "#333"
            }
          },
          "image": {
            "url": "https://via.placeholder.com/400x200/32cd32/ffffff?text=Test+Section",
            "alt": "Test section image",
            "caption": "Test section image caption"
          },
          "socialShare": {
            "enabled": true,
            "platforms": ["facebook", "whatsapp", "twitter"],
            "customMessage": "Check out this test post! 🧪✅"
          }
        }
      ],
      "conclusion": {
        "text": "This concludes our test post. If you can see this, the JSON Post Creator is working correctly!",
        "image": {
          "url": "https://via.placeholder.com/500x250/ff69b4/ffffff?text=Test+Conclusion",
          "alt": "Test conclusion image",
          "caption": "Test conclusion caption"
        }
      }
    },
    "media": {
      "featuredImage": {
        "url": "https://via.placeholder.com/800x400/ffa500/ffffff?text=Featured+Test+Image",
        "alt": "Test featured image",
        "caption": "Test featured image caption"
      }
    },
    "engagement": {
      "commentsEnabled": true,
      "socialSharing": {
        "enabled": true,
        "platforms": ["facebook", "whatsapp", "twitter", "linkedin"],
        "customShareText": "Check out this test post from JSON Creator! 🎉"
      },
      "relatedPosts": [
        {
          "title": "How to Use JSON Post Creator",
          "url": "/how-to-use-json-post-creator"
        },
        {
          "title": "Advanced JSON Techniques",
          "url": "/advanced-json-techniques"
        }
      ]
    },
    "customization": {
      "layout": "card-style",
      "colorTheme": "test-theme",
      "fontStyle": "modern-clean",
      "animations": {
        "enabled": true,
        "type": "fade-in-on-scroll"
      }
    }
  }
}

async function testJsonPostCreator() {
  try {
    console.log('Testing JSON Post Creator...')
    console.log('JSON Data:', JSON.stringify(testJsonData, null, 2))
    
    const response = await fetch('http://localhost:3001/api/admin/posts/json', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        jsonData: testJsonData,
        status: 'draft'
      })
    })

    if (response.ok) {
      const result = await response.json()
      console.log('✅ Success! Post created:', result)
    } else {
      const error = await response.json()
      console.log('❌ Error:', error)
    }
  } catch (error) {
    console.log('❌ Network Error:', error.message)
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testJsonPostCreator()
}

module.exports = { testJsonData, testJsonPostCreator }
