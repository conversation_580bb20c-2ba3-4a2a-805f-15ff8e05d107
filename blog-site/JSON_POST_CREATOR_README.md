# JSON Post Creator

The JSON Post Creator is a powerful admin tool that allows you to create blog posts using structured JSON input. This is perfect for creating complex, formatted posts with multiple sections, quotes, images, and social sharing features.

## How to Access

1. Go to the Admin Panel: `/admin`
2. Click on "JSON Post Creator" in the sidebar
3. Or use the "JSON Post Creator" button in the Quick Actions section

## JSON Structure

The JSON Post Creator expects a specific structure. Here's the basic format:

```json
{
  "blogPost": {
    "metadata": {
      "title": "Your Post Title",
      "author": "Author Name",
      "category": "Category Name",
      "tags": ["tag1", "tag2", "tag3"],
      "publishDate": "2025-07-30",
      "status": "draft",
      "seoDescription": "SEO description for the post",
      "readTime": "5 min read"
    },
    "content": {
      "introduction": {
        "text": "Introduction text...",
        "image": {
          "url": "https://example.com/image.jpg",
          "alt": "Image description",
          "caption": "Image caption"
        }
      },
      "sections": [
        {
          "id": 1,
          "type": "quote_card",
          "title": "Section Title",
          "content": {
            "quotes": [
              {
                "emoji": "✨",
                "text": "Your quote text"
              }
            ],
            "backgroundStyle": {
              "backgroundColor": "#f0f8ff",
              "borderColor": "#4a90e2",
              "textColor": "#333"
            }
          },
          "image": {
            "url": "https://example.com/section-image.jpg",
            "alt": "Section image description",
            "caption": "Section image caption"
          },
          "socialShare": {
            "enabled": true,
            "platforms": ["facebook", "whatsapp", "twitter"],
            "customMessage": "Share message"
          }
        }
      ],
      "conclusion": {
        "text": "Conclusion text...",
        "image": {
          "url": "https://example.com/conclusion-image.jpg",
          "alt": "Conclusion image description",
          "caption": "Conclusion image caption"
        }
      }
    },
    "media": {
      "featuredImage": {
        "url": "https://example.com/featured-image.jpg",
        "alt": "Featured image description",
        "caption": "Featured image caption"
      }
    },
    "engagement": {
      "commentsEnabled": true,
      "socialSharing": {
        "enabled": true,
        "platforms": ["facebook", "whatsapp", "twitter", "linkedin"],
        "customShareText": "Custom share text"
      },
      "relatedPosts": [
        {
          "title": "Related Post Title",
          "url": "/related-post-url"
        }
      ]
    },
    "customization": {
      "layout": "card-style",
      "colorTheme": "motivational-bright",
      "fontStyle": "modern-clean",
      "animations": {
        "enabled": true,
        "type": "fade-in-on-scroll"
      }
    }
  }
}
```

## Required Fields

The following fields are required for a valid post:

- `blogPost.metadata.title` - The post title
- `blogPost.metadata.author` - The author name
- `blogPost.content.sections` - Array of content sections (at least one)

## Section Types

Currently supported section types:

### quote_card
Perfect for motivational quotes, shayari, or inspirational content.

```json
{
  "id": 1,
  "type": "quote_card",
  "title": "Section Title",
  "content": {
    "quotes": [
      {
        "emoji": "✨",
        "text": "Your quote text"
      }
    ],
    "backgroundStyle": {
      "backgroundColor": "#f0f8ff",
      "borderColor": "#4a90e2",
      "textColor": "#333"
    }
  }
}
```

## Features

### 1. JSON Validation
- Real-time JSON syntax validation
- Error messages for invalid JSON
- Visual indicators for valid/invalid JSON

### 2. Preview Mode
- Preview your post structure before publishing
- See metadata, sections, and content organization
- Validate your JSON structure visually

### 3. Example Template
- Click "Use Example" to load a complete sample JSON
- Modify the example to create your own posts
- Learn the structure by examining the example

### 4. Publishing Options
- Save as Draft - Save without publishing
- Publish - Make the post live immediately
- Status is controlled by the action you choose

## How to Use

1. **Start with Example**: Click "Use Example" to load the sample JSON
2. **Modify Content**: Edit the JSON to match your content
3. **Validate**: Ensure the JSON is valid (green checkmark appears)
4. **Preview**: Click "Preview" to see how your post will look
5. **Publish**: Choose "Save Draft" or "Publish" based on your needs

## Tips

- Use the example JSON as a starting point
- Validate your JSON frequently while editing
- Preview your content before publishing
- Keep image URLs accessible and valid
- Use meaningful section titles and IDs
- Test social sharing features after publishing

## Troubleshooting

### Common Issues

1. **Invalid JSON Format**
   - Check for missing commas, brackets, or quotes
   - Use a JSON validator if needed
   - Copy the example and modify it gradually

2. **Missing Required Fields**
   - Ensure title, author, and sections are present
   - Check the error message for specific missing fields

3. **Image Loading Issues**
   - Verify image URLs are accessible
   - Use HTTPS URLs when possible
   - Provide meaningful alt text for accessibility

## API Endpoint

The JSON Post Creator uses the following API endpoint:
- **POST** `/api/admin/posts/json`
- **Body**: `{ "jsonData": {...}, "status": "draft|published" }`

This endpoint validates the JSON structure, converts it to HTML, and stores it in the database.
