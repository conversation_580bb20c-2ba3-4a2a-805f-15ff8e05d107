import { Post, User, PostWithAuthor } from '@/types/blog';

// Database service that works with both local development and production
export class DatabaseService {
  private static instance: DatabaseService;
  private db: any = null;
  private createdPosts: Post[] = []; // Store created posts in memory for development

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  // Initialize database connection (for production with Cloudflare D1)
  public setDatabase(database: any) {
    this.db = database;
  }

  // Execute query - works with both mock data and real D1
  async executeQuery(query: string, params: any[] = []): Promise<any> {
    if (this.db) {
      // Production: Use Cloudflare D1
      const stmt = this.db.prepare(query);
      if (params.length > 0) {
        return await stmt.bind(...params).all();
      }
      return await stmt.all();
    } else {
      // Development: Use mock data
      console.log('Mock query:', query, 'with params:', params);
      return await this.getMockData(query);
    }
  }

  // Mock data for local development
  private async getMockData(query: string): Promise<any> {
    if (query.includes('SELECT') && query.includes('posts')) {
      return {
        results: await this.getMockPosts()
      };
    }
    return { results: [] };
  }

  // Get mock posts data
  private async getMockPosts(): Promise<Post[]> {
    const staticPosts = [
      {
        id: 9,
        title: "501+ Best Good Night Shayari in Hindi – सुकून भरी शुभ रात्रि",
        slug: "post-4629",
        content: "Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है। जब रात का सन्नाटा फैलता है और दिल किसी खास को याद करता है, तब एक खूबसूरत शायरी आपके जज़्बातों की आवाज़ बन जाती है।",
        excerpt: "Good Night Shayari सिर्फ शुभ रात्रि कहने का एक तरीका नहीं, बल्कि दिल की गहराइयों से जुड़े जज़्बातों को बयां करने का poetic अंदाज़ है।",
        author_id: 1,
        status: "published",
        post_type: "post",
        featured_image_url: null,
        meta_title: null,
        meta_description: null,
        language: "hi",
        view_count: 0,
        comment_count: 0,
        published_at: "2025-05-21T17:19:30Z",
        created_at: "2025-07-30 08:52:42",
        updated_at: "2025-07-30 08:52:42"
      },
      {
        id: 11,
        title: "501+ Best Pyar Bhari Shayari – दिल से जुड़ी मोहब्बत",
        slug: "post-7165",
        content: "Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है। जब प्यार दिल में उतरता है, तो हर शब्द में जादू होता है, हर लफ़्ज़ में इश्क़ की खुशबू होती है।",
        excerpt: "Pyar Bhari Shayari न सिर्फ़ अल्फ़ाज़ होती है, बल्कि दिल की सबसे हसीन भावनाओं को बयां करने का सबसे खूबसूरत तरीका है।",
        author_id: 1,
        status: "published",
        post_type: "post",
        featured_image_url: null,
        meta_title: null,
        meta_description: null,
        language: "hi",
        view_count: 0,
        comment_count: 0,
        published_at: "2025-05-16T17:02:12Z",
        created_at: "2025-07-30 08:54:01",
        updated_at: "2025-07-30 08:54:01"
      },
      {
        id: 12,
        title: "301+ Best Miss You Yaad Shayari – अधूरी यादों की सच्ची आवाज़",
        slug: "post-7468",
        content: "Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं। जब कोई इतना अपना हो कि उसकी गैर-मौजूदगी भी हमें अंदर से तोड़ दे, तब हर बात में उसकी याद आने लगती है।",
        excerpt: "Miss You Yaad Shayari वो खामोशी है जिसे हम लफ़्ज़ों में नहीं कह पाते, बस महसूस करते हैं।",
        author_id: 1,
        status: "published",
        post_type: "post",
        featured_image_url: null,
        meta_title: null,
        meta_description: null,
        language: "hi",
        view_count: 0,
        comment_count: 0,
        published_at: "2025-05-16T17:02:12Z",
        created_at: "2025-07-30 08:54:36",
        updated_at: "2025-07-30 08:54:36"
      },
      {
        id: 6,
        title: "100+ Best Motivational Thoughts In Hindi – जरूर पढ़ें! ✅",
        slug: "motivational-thoughts-hindi",
        content: "मोटिवेशनल थॉट्स हिंदी (Motivational Thoughts In Hindi): यहाँ आपको 100+ Best Motivational Thoughts In Hindi मिलेंगे, जिन्हें आप फ्री में डाउनलोड कर सकते हैं। जीवन में सफलता पाने के लिए प्रेरणा बहुत जरूरी है।",
        excerpt: "मोटिवेशनल थॉट्स हिंदी (Motivational Thoughts In Hindi): यहाँ आपको 100+ Best Motivational Thoughts In Hindi मिलेंगे।",
        author_id: 1,
        status: "published",
        post_type: "post",
        featured_image_url: null,
        meta_title: null,
        meta_description: null,
        language: "hi",
        view_count: 0,
        comment_count: 0,
        published_at: "2025-02-20T02:01:47Z",
        created_at: "2025-07-30 08:34:53",
        updated_at: "2025-07-30 08:34:53"
      }
    ];

    // Combine static posts with created posts, with created posts first (newest first)
    return [...this.createdPosts.reverse(), ...staticPosts];
  }

  async getAllPosts(): Promise<Post[]> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE status = 'published'
      ORDER BY published_at DESC
    `;
    const result = await this.executeQuery(query);
    return result.results || [];
  }

  async getPostBySlug(slug: string): Promise<Post | null> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE slug = ? AND status = 'published'
    `;
    const result = await this.executeQuery(query, [slug]);
    return result.results?.[0] || null;
  }

  async getPostById(id: number): Promise<Post | null> {
    const query = `
      SELECT id, title, slug, content, excerpt, author_id, status, post_type,
             featured_image_url, meta_title, meta_description, language,
             view_count, comment_count, published_at, created_at, updated_at
      FROM posts
      WHERE id = ? AND status = 'published'
    `;
    const result = await this.executeQuery(query, [id]);
    return result.results?.[0] || null;
  }

  async getUserById(id: number): Promise<User | null> {
    const query = `
      SELECT id, username, email, display_name, bio, avatar_url, role, status,
             email_verified, created_at, updated_at
      FROM users
      WHERE id = ?
    `;
    const result = await this.executeQuery(query, [id]);

    // Return mock data for development
    if (!result.results?.[0]) {
      return {
        id: 1,
        username: "admin",
        email: "<EMAIL>",
        display_name: "Blog Admin",
        bio: "Blog administrator and content creator",
        avatar_url: null,
        role: "admin",
        status: "active",
        email_verified: true,
        created_at: "2025-01-01T00:00:00Z",
        updated_at: "2025-01-01T00:00:00Z"
      };
    }

    return result.results[0];
  }

  async createPost(postData: any): Promise<Post> {
    const now = new Date().toISOString();

    if (this.db) {
      // Production: Use Cloudflare D1
      const query = `
        INSERT INTO posts (
          title, slug, content, excerpt, author_id, status, post_type,
          featured_image_url, meta_title, meta_description, language,
          view_count, comment_count, published_at, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;

      const params = [
        postData.title,
        postData.slug,
        postData.content,
        postData.excerpt,
        postData.author_id,
        postData.status,
        postData.post_type,
        postData.featured_image_url,
        postData.meta_title,
        postData.meta_description,
        postData.language,
        postData.view_count,
        postData.comment_count,
        postData.published_at,
        now,
        now
      ];

      const result = await this.executeQuery(query, params);

      // Return the created post
      return {
        id: result.meta?.last_row_id || Math.floor(Math.random() * 10000),
        ...postData,
        created_at: now,
        updated_at: now
      };
    } else {
      // Development: Mock creation
      console.log('Mock post creation:', postData);

      // Create mock post
      const newPost = {
        id: Math.floor(Math.random() * 10000),
        title: postData.title,
        slug: postData.slug,
        content: postData.content,
        excerpt: postData.excerpt,
        author_id: postData.author_id,
        status: postData.status,
        post_type: postData.post_type,
        featured_image_url: postData.featured_image_url,
        meta_title: postData.meta_title,
        meta_description: postData.meta_description,
        language: postData.language,
        view_count: postData.view_count,
        comment_count: postData.comment_count,
        published_at: postData.published_at,
        created_at: now,
        updated_at: now
      };

      // Add to created posts array so it appears in lists
      this.createdPosts.push(newPost);

      return newPost;
    }
  }
}

export const db = DatabaseService.getInstance();
