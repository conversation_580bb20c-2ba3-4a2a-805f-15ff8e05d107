import { notFound } from 'next/navigation';
import Link from 'next/link';
import { db } from '@/lib/database';
import { PostHeader } from '@/components/post/PostHeader';
import { PostSidebar } from '@/components/post/PostSidebar';
import { ShayariDisplay } from '@/components/post/ShayariDisplay';
import { Comments } from '@/components/post/Comments';

interface PostPageProps {
  params: {
    slug: string;
  };
}

export default async function PostPage({ params }: PostPageProps) {
  const post = await db.getPostBySlug(params.slug);
  
  if (!post) {
    notFound();
  }

  const author = await db.getUserById(post.author_id);

  return (
    <>
      <nav className="breadcrumb">
        <div className="container">
          <ul className="breadcrumb-list">
            <li><Link href="/" className="breadcrumb-link">होम</Link></li>
            <li><span className="breadcrumb-current">{post.title}</span></li>
          </ul>
        </div>
      </nav>

      <main className="main-content">
        <div className="container">
          <div className="post-layout">
            <article className="post-content">
              <PostHeader post={post} author={author} />
              <ShayariDisplay post={post} author={author} />
              <Comments />
            </article>
            <PostSidebar />
          </div>
        </div>
      </main>
    </>
  );
}

export async function generateStaticParams() {
  const posts = await db.getAllPosts();
  return posts.map((post) => ({
    slug: post.slug,
  }));
}
