import type { <PERSON>ada<PERSON> } from "next";
import { Inter, Noto_Sans_Devanagari } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-family-base",
});

const interAdmin = Inter({
  subsets: ["latin"],
  variable: "--font-sans",
});

const noto_sans_devanagari = Noto_Sans_Devanagari({
  subsets: ["latin"],
  variable: "--font-family-hindi",
});

export const metadata: Metadata = {
  title: "शायरी ब्लॉग - Hindi Poetry Blog",
  description: "प्रेम, दुख, खुशी की अनमोल शायरियाँ",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="hi" suppressHydrationWarning>
      <body
        className={`${inter.variable} ${interAdmin.variable} ${noto_sans_devanagari.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}

