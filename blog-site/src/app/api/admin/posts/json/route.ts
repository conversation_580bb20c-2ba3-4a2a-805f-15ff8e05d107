import { NextRequest, NextResponse } from 'next/server'
import { db } from '@/lib/database'

// Define the expected JSON structure
interface BlogPostJson {
  blogPost: {
    metadata: {
      title: string
      author: string
      category: string
      tags?: string[]
      publishDate?: string
      status?: string
      seoDescription?: string
      readTime?: string
    }
    content: {
      introduction?: {
        text: string
        image?: {
          url: string
          alt: string
          caption?: string
        }
      }
      sections: Array<{
        id: number
        type: string
        title: string
        content: any
        image?: {
          url: string
          alt: string
          caption?: string
        }
        socialShare?: {
          enabled: boolean
          platforms: string[]
          customMessage?: string
        }
      }>
      conclusion?: {
        text: string
        image?: {
          url: string
          alt: string
          caption?: string
        }
      }
    }
    media?: {
      featuredImage?: {
        url: string
        alt: string
        caption?: string
      }
      gallery?: Array<{
        url: string
        alt: string
        caption?: string
      }>
    }
    engagement?: {
      commentsEnabled?: boolean
      socialSharing?: {
        enabled: boolean
        platforms: string[]
        customShareText?: string
      }
      relatedPosts?: Array<{
        title: string
        url: string
      }>
    }
    customization?: {
      layout?: string
      colorTheme?: string
      fontStyle?: string
      animations?: {
        enabled: boolean
        type: string
      }
    }
  }
}

function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .trim()
}

function convertJsonToHtml(jsonData: BlogPostJson): string {
  const { blogPost } = jsonData
  let html = ''

  // Add introduction with styling
  if (blogPost.content.introduction) {
    const introStyling = blogPost.content.introduction.styling
    const styleAttr = introStyling ?
      `style="font-size: ${introStyling.fontSize || '16px'}; font-weight: ${introStyling.fontWeight || '400'}; line-height: ${introStyling.lineHeight || '1.5'}; color: ${introStyling.color || '#333'}; background-color: ${introStyling.backgroundColor || 'transparent'}; padding: ${introStyling.padding || '0'}; border-radius: ${introStyling.borderRadius || '0'}; border: ${introStyling.border || 'none'};"` : ''

    html += `<div class="post-introduction" ${styleAttr}>`
    html += `<p>${blogPost.content.introduction.text}</p>`

    if (blogPost.content.introduction.image) {
      const imgStyling = blogPost.content.introduction.image.styling
      const imgStyleAttr = imgStyling ?
        `style="border-radius: ${imgStyling.borderRadius || '0'}; box-shadow: ${imgStyling.boxShadow || 'none'}; margin: ${imgStyling.margin || '0'};"` : ''

      html += `<div class="intro-image" ${imgStyleAttr}>
        <img src="${blogPost.content.introduction.image.url}" alt="${blogPost.content.introduction.image.alt}" width="${blogPost.content.introduction.image.width || 'auto'}" height="${blogPost.content.introduction.image.height || 'auto'}" />
        ${blogPost.content.introduction.image.caption ? `<p class="image-caption">${blogPost.content.introduction.image.caption}</p>` : ''}
      </div>`
    }
    html += `</div>`
  }

  // Add table of contents
  if (blogPost.content.tableOfContents?.enabled) {
    const tocStyling = blogPost.content.tableOfContents.styling
    const tocStyleAttr = tocStyling ?
      `style="background-color: ${tocStyling.backgroundColor || 'transparent'}; padding: ${tocStyling.padding || '0'}; border-radius: ${tocStyling.borderRadius || '0'}; border: ${tocStyling.border || 'none'};"` : ''

    html += `<div class="table-of-contents" ${tocStyleAttr}>`
    html += `<h3>${blogPost.content.tableOfContents.title || 'Table of Contents'}</h3>`
    html += `<ul>`
    blogPost.content.tableOfContents.items.forEach(item => {
      html += `<li><a href="${item.anchor}">${item.title}</a></li>`
    })
    html += `</ul></div>`
  }

  // Add sections with comprehensive support
  if (blogPost.content.sections) {
    blogPost.content.sections.forEach((section: any) => {
      html += `<div class="post-section" data-section-id="${section.id}" data-section-type="${section.type}">`

      // Add section heading with styling
      if (section.heading) {
        const headingStyling = section.heading.styling
        const headingStyleAttr = headingStyling ?
          `style="font-size: ${headingStyling.fontSize || '24px'}; font-weight: ${headingStyling.fontWeight || '600'}; color: ${headingStyling.color || '#333'}; margin-bottom: ${headingStyling.marginBottom || '16px'}; border-bottom: ${headingStyling.borderBottom || 'none'}; padding-bottom: ${headingStyling.paddingBottom || '0'};"` : ''

        html += `<h${section.heading.level || 2} class="section-title" ${headingStyleAttr}>${section.heading.text}</h${section.heading.level || 2}>`
      }

      // Add section description
      if (section.description) {
        const descStyling = section.description.styling
        const descStyleAttr = descStyling ?
          `style="font-size: ${descStyling.fontSize || '16px'}; color: ${descStyling.color || '#666'}; margin-bottom: ${descStyling.marginBottom || '20px'}; font-style: ${descStyling.fontStyle || 'normal'};"` : ''

        html += `<p class="section-description" ${descStyleAttr}>${section.description.text}</p>`
      }

      // Handle different content types
      if (section.content && Array.isArray(section.content)) {
        section.content.forEach((contentItem: any) => {
          if (contentItem.type === 'quote_list') {
            const listStyling = contentItem.styling
            const listStyleAttr = listStyling ?
              `style="background-color: ${listStyling.backgroundColor || 'transparent'}; padding: ${listStyling.padding || '0'}; border-radius: ${listStyling.borderRadius || '0'}; border: ${listStyling.border || 'none'};"` : ''

            html += `<div class="quote-list" ${listStyleAttr}>`
            if (contentItem.title) {
              html += `<h4 class="quote-list-title">${contentItem.title}</h4>`
            }
            html += `<${contentItem.listType === 'ordered' ? 'ol' : 'ul'} class="quote-items">`

            contentItem.items.forEach((item: any) => {
              const itemStyling = item.styling
              const itemStyleAttr = itemStyling ?
                `style="font-weight: ${itemStyling.fontWeight || '400'}; color: ${itemStyling.color || '#333'};"` : ''

              html += `<li class="quote-item" ${itemStyleAttr}>
                ${item.emoji ? `<span class="quote-emoji">${item.emoji}</span>` : ''}
                <span class="quote-text">${item.text}</span>
              </li>`
            })
            html += `</${contentItem.listType === 'ordered' ? 'ol' : 'ul'}></div>`
          }

          if (contentItem.type === 'subsection') {
            if (contentItem.heading) {
              const subHeadingStyling = contentItem.heading.styling
              const subHeadingStyleAttr = subHeadingStyling ?
                `style="font-size: ${subHeadingStyling.fontSize || '20px'}; font-weight: ${subHeadingStyling.fontWeight || '600'}; color: ${subHeadingStyling.color || '#333'}; margin-top: ${subHeadingStyling.marginTop || '20px'}; margin-bottom: ${subHeadingStyling.marginBottom || '15px'};"` : ''

              html += `<h${contentItem.heading.level || 3} class="subsection-title" ${subHeadingStyleAttr}>${contentItem.heading.text}</h${contentItem.heading.level || 3}>`
            }

            // Process subsection content recursively
            if (contentItem.content && Array.isArray(contentItem.content)) {
              contentItem.content.forEach((subItem: any) => {
                if (subItem.type === 'quote_list') {
                  const subListStyling = subItem.styling
                  const subListStyleAttr = subListStyling ?
                    `style="background-color: ${subListStyling.backgroundColor || 'transparent'}; padding: ${subListStyling.padding || '0'}; border-radius: ${subListStyling.borderRadius || '0'};"` : ''

                  html += `<div class="quote-list" ${subListStyleAttr}>`
                  html += `<${subItem.listType === 'ordered' ? 'ol' : 'ul'} class="quote-items">`

                  subItem.items.forEach((item: any) => {
                    html += `<li class="quote-item">
                      ${item.emoji ? `<span class="quote-emoji">${item.emoji}</span>` : ''}
                      <span class="quote-text">${item.text}</span>
                    </li>`
                  })
                  html += `</${subItem.listType === 'ordered' ? 'ol' : 'ul'}></div>`
                }
              })
            }
          }
        })
      }

      // Handle FAQ sections
      if (section.type === 'faq_section' && section.faqs) {
        html += `<div class="faq-section">`
        section.faqs.forEach((faq: any) => {
          const faqStyling = faq.styling
          const faqStyleAttr = faqStyling ?
            `style="background-color: ${faqStyling.backgroundColor || 'transparent'}; padding: ${faqStyling.padding || '0'}; border-radius: ${faqStyling.borderRadius || '0'}; margin-bottom: ${faqStyling.marginBottom || '15px'};"` : ''

          html += `<div class="faq-item" ${faqStyleAttr}>
            <h4 class="faq-question" style="color: ${faqStyling?.questionColor || '#333'};">${faq.question}</h4>
            <p class="faq-answer" style="color: ${faqStyling?.answerColor || '#666'};">${faq.answer}</p>
          </div>`
        })
        html += `</div>`
      }

      // Add section image
      if (section.image) {
        const imgStyling = section.image.styling
        const imgStyleAttr = imgStyling ?
          `style="border-radius: ${imgStyling.borderRadius || '0'}; margin: ${imgStyling.margin || '0'}; box-shadow: ${imgStyling.boxShadow || 'none'};"` : ''

        html += `<div class="section-image" ${imgStyleAttr}>
          <img src="${section.image.url}" alt="${section.image.alt}" width="${section.image.width || 'auto'}" height="${section.image.height || 'auto'}" />
          ${section.image.caption ? `<p class="image-caption">${section.image.caption}</p>` : ''}
        </div>`
      }

      // Add call to action
      if (section.callToAction?.enabled) {
        const ctaStyling = section.callToAction.styling
        const ctaStyleAttr = ctaStyling ?
          `style="background-color: ${ctaStyling.backgroundColor || 'transparent'}; padding: ${ctaStyling.padding || '0'}; border-radius: ${ctaStyling.borderRadius || '0'}; border: ${ctaStyling.border || 'none'}; margin-top: ${ctaStyling.marginTop || '20px'}; font-weight: ${ctaStyling.fontWeight || '400'};"` : ''

        html += `<div class="call-to-action" ${ctaStyleAttr}>${section.callToAction.text}</div>`
      }

      html += `</div>`
    })
  }

  // Add conclusion with styling
  if (blogPost.content.conclusion) {
    const conclusionStyling = blogPost.content.conclusion.styling
    const conclusionStyleAttr = conclusionStyling ?
      `style="font-size: ${conclusionStyling.fontSize || '16px'}; line-height: ${conclusionStyling.lineHeight || '1.5'}; color: ${conclusionStyling.color || '#333'}; background-color: ${conclusionStyling.backgroundColor || 'transparent'}; padding: ${conclusionStyling.padding || '0'}; border-radius: ${conclusionStyling.borderRadius || '0'};"` : ''

    html += `<div class="post-conclusion" ${conclusionStyleAttr}>`

    // Add conclusion heading if present
    if (blogPost.content.conclusion.heading) {
      const headingStyling = blogPost.content.conclusion.heading.styling
      const headingStyleAttr = headingStyling ?
        `style="font-size: ${headingStyling.fontSize || '24px'}; font-weight: ${headingStyling.fontWeight || '700'}; color: ${headingStyling.color || '#333'};"` : ''

      html += `<h${blogPost.content.conclusion.heading.level || 2} class="conclusion-title" ${headingStyleAttr}>${blogPost.content.conclusion.heading.text}</h${blogPost.content.conclusion.heading.level || 2}>`
    }

    html += `<p>${blogPost.content.conclusion.text}</p>`

    if (blogPost.content.conclusion.image) {
      html += `<div class="conclusion-image">
        <img src="${blogPost.content.conclusion.image.url}" alt="${blogPost.content.conclusion.image.alt}" />
        ${blogPost.content.conclusion.image.caption ? `<p class="image-caption">${blogPost.content.conclusion.image.caption}</p>` : ''}
      </div>`
    }
    html += `</div>`
  }

  // Add engagement features
  if (blogPost.engagement) {
    html += `<div class="post-engagement">`

    if (blogPost.engagement.socialSharing?.enabled) {
      html += `<div class="post-social-sharing">
        <h4>Share this post:</h4>
        <div class="social-buttons">
          ${blogPost.engagement.socialSharing.platforms.map(platform =>
            `<button class="social-btn social-${platform}" data-platform="${platform}">${platform}</button>`
          ).join('')}
        </div>
      </div>`
    }

    if (blogPost.engagement.relatedPosts && blogPost.engagement.relatedPosts.length > 0) {
      html += `<div class="related-posts">
        <h4>Related Posts:</h4>
        <ul>
          ${blogPost.engagement.relatedPosts.map(post =>
            `<li><a href="${post.url}">${post.title}</a></li>`
          ).join('')}
        </ul>
      </div>`
    }

    html += `</div>`
  }

  return html
}

function generateExcerpt(jsonData: BlogPostJson): string {
  const { blogPost } = jsonData
  
  // Try to get excerpt from introduction
  if (blogPost.content.introduction?.text) {
    return blogPost.content.introduction.text.substring(0, 200) + '...'
  }
  
  // Try to get excerpt from SEO description
  if (blogPost.metadata.seoDescription) {
    return blogPost.metadata.seoDescription
  }
  
  // Try to get excerpt from first section
  if (blogPost.content.sections && blogPost.content.sections.length > 0) {
    const firstSection = blogPost.content.sections[0]
    if (firstSection.content.text) {
      return firstSection.content.text.substring(0, 200) + '...'
    }
    if (firstSection.content.quotes && firstSection.content.quotes.length > 0) {
      return firstSection.content.quotes.map((q: any) => q.text).join(' ').substring(0, 200) + '...'
    }
  }
  
  return blogPost.metadata.title
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { jsonData, status = 'draft' } = body

    if (!jsonData || !jsonData.blogPost) {
      return NextResponse.json(
        { error: 'Invalid JSON structure. Expected blogPost object.' },
        { status: 400 }
      )
    }

    const { blogPost } = jsonData as BlogPostJson

    // Validate required fields
    if (!blogPost.metadata?.title) {
      return NextResponse.json(
        { error: 'Missing required field: metadata.title' },
        { status: 400 }
      )
    }

    if (!blogPost.metadata?.author) {
      return NextResponse.json(
        { error: 'Missing required field: metadata.author' },
        { status: 400 }
      )
    }

    if (!blogPost.content?.sections || blogPost.content.sections.length === 0) {
      return NextResponse.json(
        { error: 'Missing required field: content.sections' },
        { status: 400 }
      )
    }

    // Generate slug from title
    const slug = generateSlug(blogPost.metadata.title)
    
    // Convert JSON to HTML content
    const htmlContent = convertJsonToHtml(jsonData)
    
    // Generate excerpt
    const excerpt = generateExcerpt(jsonData)
    
    // Get featured image URL
    const featuredImageUrl = blogPost.media?.featuredImage?.url || null
    
    // Prepare post data
    const postData = {
      title: blogPost.metadata.title,
      slug: slug,
      content: htmlContent,
      excerpt: excerpt,
      author_id: 1, // Default admin user
      status: status,
      post_type: 'post',
      featured_image_url: featuredImageUrl,
      meta_title: blogPost.metadata.title,
      meta_description: blogPost.metadata.seoDescription || excerpt,
      language: 'hi', // Default to Hindi
      view_count: 0,
      comment_count: 0,
      published_at: status === 'published' ? new Date().toISOString() : null,
      // Store original JSON data as metadata
      json_metadata: JSON.stringify(jsonData)
    }

    // Create the post using database service
    const result = await db.createPost(postData)

    return NextResponse.json({
      success: true,
      message: 'Post created successfully',
      post: result
    })

  } catch (error) {
    console.error('Error creating post from JSON:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
