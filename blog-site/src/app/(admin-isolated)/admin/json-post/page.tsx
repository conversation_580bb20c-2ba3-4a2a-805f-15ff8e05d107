'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { 
  Save, 
  Send, 
  ArrowLeft, 
  Code, 
  Eye, 
  CheckCircle, 
  AlertCircle,
  Copy,
  FileText
} from "lucide-react"
import Link from "next/link"

export default function JsonPostPage() {
  const router = useRouter()
  const [jsonInput, setJsonInput] = useState('')
  const [isValidJson, setIsValidJson] = useState(true)
  const [jsonError, setJsonError] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [previewData, setPreviewData] = useState<any>(null)
  const [showPreview, setShowPreview] = useState(false)

  // Comprehensive JSON structure with maximum customization
  const exampleJson = {
    "blogPost": {
      "metadata": {
        "title": "401+ Best Quotes for Instagram Bio – Short बातें, Cool Look",
        "h1": "Best Quotes for Instagram Bio",
        "slug": "best-quotes-instagram-bio-short-cool",
        "author": {
          "name": "Your Name",
          "bio": "Content Creator & Social Media Expert",
          "avatar": {
            "url": "https://example.com/author-avatar.jpg",
            "alt": "Author profile picture",
            "width": 150,
            "height": 150
          },
          "socialLinks": {
            "instagram": "https://instagram.com/yourhandle",
            "twitter": "https://twitter.com/yourhandle"
          }
        },
        "publishDate": "2025-07-30T10:00:00Z",
        "lastModified": "2025-07-30T15:30:00Z",
        "status": "published",
        "category": {
          "name": "Social Media",
          "slug": "social-media",
          "color": "#4a90e2"
        },
        "tags": [
          {
            "name": "Instagram",
            "slug": "instagram",
            "color": "#e4405f"
          },
          {
            "name": "Quotes",
            "slug": "quotes",
            "color": "#34a853"
          }
        ],
        "readTime": "8 min read",
        "wordCount": 2500,
        "language": "hi-en",
        "seo": {
          "metaTitle": "401+ Best Instagram Bio Quotes - Short & Cool Ideas 2025",
          "metaDescription": "Discover 401+ best quotes for Instagram bio in Hindi & English. Short, cool, funny quotes for boys & girls.",
          "keywords": ["instagram bio quotes", "best quotes for instagram bio", "short instagram bio"],
          "canonicalUrl": "https://yourdomain.com/best-quotes-instagram-bio-short-cool",
          "ogTitle": "401+ Best Quotes for Instagram Bio – Short बातें, Cool Look",
          "ogDescription": "Get the best Instagram bio quotes collection!",
          "ogImage": {
            "url": "https://example.com/og-instagram-bio-quotes.jpg",
            "alt": "Best Instagram Bio Quotes Collection",
            "width": 1200,
            "height": 630
          }
        }
      },
      "featuredMedia": {
        "image": {
          "url": "https://example.com/featured-instagram-bio-quotes.jpg",
          "alt": "Best Instagram Bio Quotes - Featured Image",
          "caption": "Discover the perfect Instagram bio quote for your profile",
          "width": 1200,
          "height": 675,
          "format": "webp",
          "sizes": {
            "thumbnail": {
              "url": "https://example.com/featured-thumb.jpg",
              "width": 300,
              "height": 169
            },
            "medium": {
              "url": "https://example.com/featured-medium.jpg",
              "width": 600,
              "height": 338
            }
          }
        }
      },
      "content": {
        "introduction": {
          "text": "Instagram bio आपकी digital personality का पहला impression है। एक perfect bio quote आपको crowd से अलग बनाता है।",
          "styling": {
            "fontSize": "18px",
            "fontWeight": "400",
            "lineHeight": "1.6",
            "color": "#333333",
            "backgroundColor": "#f8f9fa",
            "padding": "20px",
            "borderRadius": "8px"
          },
          "image": {
            "url": "https://example.com/intro-instagram-bio.jpg",
            "alt": "Instagram Bio Introduction",
            "caption": "Perfect Instagram bio makes the first impression count",
            "width": 800,
            "height": 400,
            "position": "after-text",
            "styling": {
              "borderRadius": "12px",
              "boxShadow": "0 4px 12px rgba(0,0,0,0.1)",
              "margin": "20px 0"
            }
          }
        },
        "tableOfContents": {
          "enabled": true,
          "title": "📋 Table of Contents",
          "styling": {
            "backgroundColor": "#f1f3f4",
            "padding": "20px",
            "borderRadius": "8px",
            "border": "2px solid #4285f4"
          },
          "items": [
            {
              "title": "Short Best Quotes for Instagram Bio",
              "anchor": "#short-quotes",
              "level": 2
            },
            {
              "title": "Life Quotes for Instagram Bio",
              "anchor": "#life-quotes",
              "level": 2
            }
          ]
        },
        "sections": [
          {
            "id": "short-quotes",
            "type": "content_section",
            "heading": {
              "level": 2,
              "text": "Short Best Quotes for Instagram Bio",
              "styling": {
                "fontSize": "28px",
                "fontWeight": "700",
                "color": "#1a73e8",
                "marginBottom": "16px",
                "borderBottom": "3px solid #4285f4",
                "paddingBottom": "8px"
              }
            },
            "description": {
              "text": "Short, impactful quotes for Instagram bio that make a lasting impression in just a few words.",
              "styling": {
                "fontSize": "16px",
                "color": "#5f6368",
                "marginBottom": "20px",
                "fontStyle": "italic"
              }
            },
            "content": [
              {
                "type": "quote_list",
                "title": "✨ Powerful Short Quotes",
                "listType": "unordered",
                "styling": {
                  "backgroundColor": "#fff3e0",
                  "padding": "20px",
                  "borderRadius": "8px",
                  "border": "1px solid #ffb74d"
                },
                "items": [
                  {
                    "text": "Dream big, work hard 💪",
                    "emoji": "✨",
                    "styling": {
                      "fontWeight": "500",
                      "color": "#e65100"
                    }
                  },
                  {
                    "text": "Stay humble, hustle hard 🚀",
                    "emoji": "🔥",
                    "styling": {
                      "fontWeight": "500",
                      "color": "#e65100"
                    }
                  },
                  {
                    "text": "Born to shine ⭐",
                    "emoji": "✨",
                    "styling": {
                      "fontWeight": "500",
                      "color": "#e65100"
                    }
                  }
                ]
              }
            ],
            "image": {
              "url": "https://example.com/short-quotes-section.jpg",
              "alt": "Short Instagram Bio Quotes Collection",
              "caption": "Perfect short quotes for your Instagram bio",
              "width": 600,
              "height": 400,
              "position": "after-content",
              "styling": {
                "borderRadius": "10px",
                "margin": "25px 0",
                "boxShadow": "0 6px 20px rgba(0,0,0,0.15)"
              }
            }
          },
          {
            "id": "faqs",
            "type": "faq_section",
            "heading": {
              "level": 2,
              "text": "✔️ Best Quotes for Instagram Bio – अक्सर पूछे जाने वाले सवाल (FAQs)",
              "styling": {
                "fontSize": "26px",
                "fontWeight": "700",
                "color": "#d32f2f",
                "marginBottom": "20px"
              }
            },
            "faqs": [
              {
                "question": "Instagram bio में good quotes क्यों ज़रूरी होते हैं?",
                "answer": "Instagram bio आपकी पहली impression होती है। एक अच्छा quote आपकी personality को reflect करता है और visitors को आपको follow करने के लिए motivate करता है।",
                "styling": {
                  "questionColor": "#1976d2",
                  "answerColor": "#424242",
                  "backgroundColor": "#f5f5f5",
                  "padding": "15px",
                  "borderRadius": "8px",
                  "marginBottom": "15px"
                }
              },
              {
                "question": "सबसे ज्यादा पसंद किए जाने वाले bio quotes कौन से हैं?",
                "answer": "Short, motivational और attitude-filled quotes सबसे ज़्यादा पसंद किए जाते हैं। जैसे 'Dream big, work hard' या 'Stay humble, hustle hard' जैसे quotes popular हैं।",
                "styling": {
                  "questionColor": "#1976d2",
                  "answerColor": "#424242",
                  "backgroundColor": "#f5f5f5",
                  "padding": "15px",
                  "borderRadius": "8px",
                  "marginBottom": "15px"
                }
              }
            ]
          },
          {
            "id": 3,
            "type": "quote_card",
            "title": "Hustle and Believe",
            "content": {
              "quotes": [
                {
                  "emoji": "💥",
                  "text": "Hustle in silence,"
                },
                {
                  "emoji": "🌸",
                  "text": "Let success shout,"
                },
                {
                  "emoji": "🌿",
                  "text": "Believe always,"
                },
                {
                  "emoji": "⚡",
                  "text": "Never doubt."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#f0fff0",
                "borderColor": "#32cd32",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/hustle-believe-image.jpg",
              "alt": "Success celebration",
              "caption": "Silent work, loud results"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Hustle in silence, let success shout! 💥🌸"
            }
          },
          {
            "id": 4,
            "type": "quote_card",
            "title": "Spread Kindness",
            "content": {
              "quotes": [
                {
                  "emoji": "🌻",
                  "text": "Spread kindness,"
                },
                {
                  "emoji": "😊",
                  "text": "Choose joy,"
                },
                {
                  "emoji": "💙",
                  "text": "Stay grateful,"
                },
                {
                  "emoji": "🌱",
                  "text": "Be you."
                }
              ],
              "backgroundStyle": {
                "backgroundColor": "#fff0f5",
                "borderColor": "#ff69b4",
                "textColor": "#333"
              }
            },
            "image": {
              "url": "https://example.com/kindness-image.jpg",
              "alt": "Acts of kindness",
              "caption": "Small acts, big impact"
            },
            "socialShare": {
              "enabled": true,
              "platforms": ["facebook", "whatsapp", "twitter"],
              "customMessage": "Spread kindness and choose joy! 🌻😊"
            }
          }
        ],
        "conclusion": {
          "heading": {
            "level": 2,
            "text": "📌 निष्कर्ष: अपनी Instagram Bio को बनाए Short, Smart और Stylish",
            "styling": {
              "fontSize": "24px",
              "fontWeight": "700",
              "color": "#2e7d32"
            }
          },
          "text": "Instagram bio कुछ शब्दों में आपकी पूरी personality को दिखाने का ज़रिया है। इन quotes में से अपने लिए perfect quote choose करें।",
          "styling": {
            "fontSize": "16px",
            "lineHeight": "1.6",
            "color": "#333",
            "backgroundColor": "#e8f5e8",
            "padding": "20px",
            "borderRadius": "8px"
          }
        }
      },
      "engagement": {
        "commentsEnabled": true,
        "socialSharing": {
          "enabled": true,
          "platforms": ["facebook", "whatsapp", "twitter", "linkedin", "pinterest"],
          "customShareText": "Check out these amazing Instagram bio quotes! 🌟",
          "styling": {
            "backgroundColor": "#f8f9fa",
            "padding": "20px",
            "borderRadius": "8px",
            "marginTop": "30px"
          }
        },
        "relatedPosts": [
          {
            "title": "Best Instagram Captions for Photos",
            "url": "/best-instagram-captions-photos",
            "thumbnail": "https://example.com/related-captions.jpg"
          },
          {
            "title": "How to Grow Instagram Followers Organically",
            "url": "/grow-instagram-followers-organically",
            "thumbnail": "https://example.com/related-growth.jpg"
          }
        ],
        "newsletter": {
          "enabled": true,
          "title": "Get More Social Media Tips!",
          "description": "Subscribe for weekly tips on Instagram growth and content creation.",
          "styling": {
            "backgroundColor": "#e3f2fd",
            "padding": "25px",
            "borderRadius": "10px",
            "border": "2px solid #2196f3"
          }
        }
      },
      "customization": {
        "layout": "magazine-style",
        "colorTheme": "vibrant-social",
        "fontFamily": "Inter, sans-serif",
        "animations": {
          "enabled": true,
          "type": "fade-in-on-scroll",
          "duration": "0.6s"
        },
        "responsiveBreakpoints": {
          "mobile": "768px",
          "tablet": "1024px",
          "desktop": "1200px"
        }
      }
    }
  }

  const validateJson = (jsonString: string) => {
    try {
      const parsed = JSON.parse(jsonString)
      setIsValidJson(true)
      setJsonError('')
      setPreviewData(parsed)
      return true
    } catch (error) {
      setIsValidJson(false)
      setJsonError(error instanceof Error ? error.message : 'Invalid JSON format')
      setPreviewData(null)
      return false
    }
  }

  const handleJsonChange = (value: string) => {
    setJsonInput(value)
    if (value.trim()) {
      validateJson(value)
    } else {
      setIsValidJson(true)
      setJsonError('')
      setPreviewData(null)
    }
  }

  const handleSave = async (saveStatus: string) => {
    if (!jsonInput.trim()) {
      setJsonError('Please enter JSON data')
      return
    }

    if (!validateJson(jsonInput)) {
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/admin/posts/json', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          jsonData: JSON.parse(jsonInput),
          status: saveStatus
        })
      })

      if (response.ok) {
        const result = await response.json()
        console.log('Post created:', result)
        router.push('/admin/posts')
      } else {
        const error = await response.json()
        setJsonError(error.message || 'Failed to create post')
      }
    } catch (error) {
      setJsonError('Failed to create post. Please try again.')
      console.error('Error creating post:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const copyExampleJson = () => {
    const jsonString = JSON.stringify(exampleJson, null, 2)
    navigator.clipboard.writeText(jsonString)
    setJsonInput(jsonString)
    validateJson(jsonString)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/posts">
            <button className="btn btn-ghost btn-icon">
              <ArrowLeft className="h-4 w-4" />
            </button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">JSON Post Creator</h1>
            <p className="text-muted-foreground">Create blog posts using JSON input</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="btn btn-outline"
            onClick={() => setShowPreview(!showPreview)}
            disabled={!previewData}
          >
            <Eye className="mr-2 h-4 w-4" />
            {showPreview ? 'Hide Preview' : 'Preview'}
          </button>
          <button
            className="btn btn-outline"
            onClick={() => handleSave('draft')}
            disabled={isLoading || !isValidJson}
          >
            <Save className="mr-2 h-4 w-4" />
            {isLoading ? 'Saving...' : 'Save Draft'}
          </button>
          <button
            className="btn btn-primary"
            onClick={() => handleSave('published')}
            disabled={isLoading || !isValidJson}
          >
            <Send className="mr-2 h-4 w-4" />
            {isLoading ? 'Publishing...' : 'Publish'}
          </button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* JSON Input */}
        <div className="space-y-4">
          <div className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="card-title flex items-center">
                    <Code className="mr-2 h-4 w-4" />
                    JSON Input
                  </h3>
                  <p className="card-description">Paste your blog post JSON data here</p>
                </div>
                <button
                  className="btn btn-outline btn-sm"
                  onClick={copyExampleJson}
                >
                  <Copy className="mr-2 h-3 w-3" />
                  Use Example
                </button>
              </div>
            </div>
            <div className="card-content">
              <div className="space-y-2">
                <textarea
                  value={jsonInput}
                  onChange={(e) => handleJsonChange(e.target.value)}
                  placeholder="Paste your JSON data here..."
                  className={`input min-h-[400px] font-mono text-sm ${
                    !isValidJson ? 'border-red-500' : ''
                  }`}
                  style={{ resize: 'vertical' }}
                />
                {jsonError && (
                  <div className="flex items-center space-x-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    <span>{jsonError}</span>
                  </div>
                )}
                {isValidJson && jsonInput && (
                  <div className="flex items-center space-x-2 text-green-600 text-sm">
                    <CheckCircle className="h-4 w-4" />
                    <span>Valid JSON format</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Preview/Instructions */}
        <div className="space-y-4">
          {showPreview && previewData ? (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </h3>
                <p className="card-description">Preview of your blog post data</p>
              </div>
              <div className="card-content">
                <div className="space-y-4">
                  {previewData.blogPost?.metadata && (
                    <div>
                      <h4 className="font-semibold text-lg">
                        {previewData.blogPost.metadata.title}
                      </h4>
                      <p className="text-sm text-muted-foreground">
                        By {previewData.blogPost.metadata.author} • {previewData.blogPost.metadata.category}
                      </p>
                      <p className="text-sm mt-2">
                        {previewData.blogPost.metadata.seoDescription}
                      </p>
                      {previewData.blogPost.metadata.tags && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {previewData.blogPost.metadata.tags.map((tag: string, index: number) => (
                            <span key={index} className="badge badge-secondary text-xs">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                  
                  {previewData.blogPost?.content?.sections && (
                    <div>
                      <h5 className="font-medium mb-2">Sections ({previewData.blogPost.content.sections.length})</h5>
                      <div className="space-y-2">
                        {previewData.blogPost.content.sections.map((section: any, index: number) => (
                          <div key={index} className="p-2 bg-accent rounded text-sm">
                            <strong>{section.title || `Section ${section.id}`}</strong>
                            <span className="text-muted-foreground ml-2">({section.type})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="card">
              <div className="card-header">
                <h3 className="card-title flex items-center">
                  <FileText className="mr-2 h-4 w-4" />
                  Instructions
                </h3>
                <p className="card-description">How to use the JSON Post Creator</p>
              </div>
              <div className="card-content">
                <div className="space-y-4 text-sm">
                  <div>
                    <h4 className="font-medium mb-2">1. JSON Structure</h4>
                    <p className="text-muted-foreground">
                      Your JSON should contain a "blogPost" object with "metadata" and "content" sections.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">2. Required Fields</h4>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1">
                      <li>metadata.title - Post title</li>
                      <li>metadata.author - Author name</li>
                      <li>metadata.category - Post category</li>
                      <li>content.sections - Array of content sections</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">3. Quick Start</h4>
                    <p className="text-muted-foreground">
                      Click "Use Example" to load a sample JSON structure that you can modify.
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium mb-2">4. Validation</h4>
                    <p className="text-muted-foreground">
                      The system will validate your JSON format and show any errors below the input area.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
