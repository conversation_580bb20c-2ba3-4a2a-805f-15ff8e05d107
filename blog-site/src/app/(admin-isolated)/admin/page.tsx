'use client'

import {
  FileText,
  Eye,
  FolderOpen,
  Users,
  TrendingUp,
  Plus,
  Settings,
  Calendar,
  MoreHorizontal,
  Code
} from "lucide-react"
import Link from "next/link"

const stats = [
  {
    title: "Total Posts",
    value: "24",
    change: "+2 from last month",
    icon: FileText,
  },
  {
    title: "Total Views",
    value: "12,543",
    change: "+15% from last month",
    icon: Eye,
  },
  {
    title: "Categories",
    value: "8",
    change: "+1 new category",
    icon: FolderOpen,
  },
  {
    title: "Active Users",
    value: "1,234",
    change: "+8% from last month",
    icon: Users,
  },
]

const recentPosts = [
  {
    id: 1,
    title: "501+ Best Good Night Shayari in Hindi",
    status: "published",
    views: 1234,
  },
  {
    id: 2,
    title: "501+ Best Pyar Bhari Shayari",
    status: "published",
    views: 987,
  },
  {
    id: 3,
    title: "301+ Best Miss You <PERSON><PERSON>",
    status: "draft",
    views: 0,
  },
  {
    id: 4,
    title: "100+ Best Motivational Thoughts",
    status: "published",
    views: 2341,
  },
]

export default function AdminDashboard() {
  return (
    <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
          <p className="text-muted-foreground">Welcome to your blog administration panel</p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <div key={stat.title} className="card">
              <div className="card-header flex flex-row items-center justify-between space-y-0 pb-2">
                <h3 className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </h3>
                <stat.icon className="h-4 w-4 text-muted-foreground" />
              </div>
              <div className="card-content">
                <div className="text-2xl font-bold text-foreground">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  {stat.change}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Recent Posts */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Recent Posts</h3>
              <p className="card-description">Your latest blog posts and their performance</p>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {recentPosts.map((post) => (
                  <div key={post.id} className="flex items-center justify-between">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-foreground">{post.title}</p>
                      <div className="flex items-center space-x-2">
                        <span
                          className={`badge ${post.status === 'published' ? 'badge-default' : 'badge-secondary'} text-xs`}
                        >
                          {post.status}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {post.views} views
                        </span>
                      </div>
                    </div>
                    <button className="btn btn-ghost btn-icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Quick Actions</h3>
              <p className="card-description">Common administrative tasks</p>
            </div>
            <div className="card-content">
              <div className="space-y-3">
                <Link href="/admin/posts/new">
                  <button className="btn btn-primary w-full justify-start">
                    <Plus className="mr-2 h-4 w-4" />
                    Create New Post
                  </button>
                </Link>
                <Link href="/admin/json-post">
                  <button className="btn btn-secondary w-full justify-start">
                    <Code className="mr-2 h-4 w-4" />
                    JSON Post Creator
                  </button>
                </Link>
                <Link href="/admin/categories">
                  <button className="btn btn-outline w-full justify-start">
                    <FolderOpen className="mr-2 h-4 w-4" />
                    Manage Categories
                  </button>
                </Link>
                <Link href="/admin/scheduled">
                  <button className="btn btn-outline w-full justify-start">
                    <Calendar className="mr-2 h-4 w-4" />
                    Schedule Posts
                  </button>
                </Link>
              </div>
            </div>
          </div>
        </div>
    </div>
  )
}
