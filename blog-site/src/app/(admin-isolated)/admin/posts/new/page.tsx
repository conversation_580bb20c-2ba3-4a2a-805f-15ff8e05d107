'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import {
  ArrowLeft,
  Save,
  Send,
  Eye,
  ExternalLink,
  Search
} from "lucide-react"
import Link from 'next/link'

export default function NewPostPage() {
  const router = useRouter()
  const [title, setTitle] = useState('')
  const [slug, setSlug] = useState('')
  const [excerpt, setExcerpt] = useState('')
  const [content, setContent] = useState('')
  const [metaTitle, setMetaTitle] = useState('')
  const [metaDescription, setMetaDescription] = useState('')
  const [keywords, setKeywords] = useState('')
  const [status, setStatus] = useState('draft')
  const [category, setCategory] = useState('')
  const [featuredImage, setFeaturedImage] = useState('')
  const [isScheduled, setIsScheduled] = useState(false)
  const [publishDate, setPublishDate] = useState('')

  const handleSave = async (saveStatus: string) => {
    console.log('Saving post:', {
      title, slug, content, excerpt, category,
      status: saveStatus, featuredImage, metaTitle,
      metaDescription, keywords, isScheduled, publishDate,
    })
    router.push('/admin/posts')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/admin/posts">
            <button className="btn btn-ghost btn-icon">
              <ArrowLeft className="h-4 w-4" />
            </button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Create New Post</h1>
            <p className="text-muted-foreground">Write and publish a new blog post</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            className="btn btn-outline"
            onClick={() => handleSave('draft')}
          >
            <Save className="mr-2 h-4 w-4" />
            Save Draft
          </button>
          <button
            className="btn btn-primary"
            onClick={() => handleSave('published')}
          >
            <Send className="mr-2 h-4 w-4" />
            Publish
          </button>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Post Content */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Post Content</h3>
              <p className="card-description">Write your blog post content and basic information</p>
            </div>
            <div className="card-content space-y-4">
              <div>
                <label htmlFor="title" className="label">Title</label>
                <input
                  id="title"
                  type="text"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  placeholder="Enter post title"
                  className="input"
                />
              </div>
              <div>
                <label htmlFor="slug" className="label">URL Slug</label>
                <input
                  id="slug"
                  type="text"
                  value={slug}
                  onChange={(e) => setSlug(e.target.value)}
                  placeholder="post-url-slug"
                  className="input"
                />
              </div>
              <div>
                <label htmlFor="excerpt" className="label">Excerpt</label>
                <textarea
                  id="excerpt"
                  value={excerpt}
                  onChange={(e) => setExcerpt(e.target.value)}
                  placeholder="Brief description of the post"
                  className="input textarea"
                  rows={3}
                />
              </div>
              <div>
                <label htmlFor="content" className="label">Content</label>
                <textarea
                  id="content"
                  value={content}
                  onChange={(e) => setContent(e.target.value)}
                  placeholder="Write your post content here..."
                  className="input textarea"
                  rows={12}
                />
              </div>
            </div>
          </div>

          {/* SEO Settings */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>SEO Settings</span>
              </h3>
              <p className="card-description">Optimize your post for search engines</p>
            </div>
            <div className="card-content space-y-4">
              <div>
                <label htmlFor="metaTitle" className="label">Meta Title</label>
                <input
                  id="metaTitle"
                  type="text"
                  value={metaTitle}
                  onChange={(e) => setMetaTitle(e.target.value)}
                  placeholder="SEO title for search engines"
                  className="input"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {metaTitle.length}/60 characters
                </p>
              </div>
              <div>
                <label htmlFor="metaDescription" className="label">Meta Description</label>
                <textarea
                  id="metaDescription"
                  value={metaDescription}
                  onChange={(e) => setMetaDescription(e.target.value)}
                  placeholder="Brief description for search results"
                  className="input textarea"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  {metaDescription.length}/160 characters
                </p>
              </div>
              <div>
                <label htmlFor="keywords" className="label">Keywords</label>
                <input
                  id="keywords"
                  type="text"
                  value={keywords}
                  onChange={(e) => setKeywords(e.target.value)}
                  placeholder="keyword1, keyword2, keyword3"
                  className="input"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Publish Settings */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Publish Settings</h3>
              <p className="card-description">Configure publication options</p>
            </div>
            <div className="card-content space-y-4">
              <div>
                <label htmlFor="status" className="label">Status</label>
                <select
                  value={status}
                  onChange={(e) => setStatus(e.target.value)}
                  className="select"
                >
                  <option value="draft">Draft</option>
                  <option value="published">Published</option>
                </select>
              </div>
              <div>
                <label htmlFor="category" className="label">Category</label>
                <select
                  value={category}
                  onChange={(e) => setCategory(e.target.value)}
                  className="select"
                >
                  <option value="">Select category</option>
                  <option value="good-night">Good Night</option>
                  <option value="love">Love Shayari</option>
                  <option value="sad">Sad Shayari</option>
                  <option value="motivational">Motivational</option>
                </select>
              </div>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="schedule"
                  checked={isScheduled}
                  onChange={(e) => setIsScheduled(e.target.checked)}
                  className="checkbox"
                />
                <label htmlFor="schedule" className="label">Schedule for later</label>
              </div>
            </div>
          </div>

          {/* Featured Image */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Featured Image</h3>
              <p className="card-description">Set a featured image for your post</p>
            </div>
            <div className="card-content">
              <div>
                <label htmlFor="featuredImage" className="label">Image URL</label>
                <input
                  id="featuredImage"
                  type="text"
                  value={featuredImage}
                  onChange={(e) => setFeaturedImage(e.target.value)}
                  placeholder="https://example.com/image.jpg"
                  className="input"
                />
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card">
            <div className="card-header">
              <h3 className="card-title">Quick Actions</h3>
            </div>
            <div className="card-content space-y-2">
              <button className="btn btn-outline w-full justify-start">
                <Eye className="mr-2 h-4 w-4" />
                Preview
              </button>
              <button className="btn btn-outline w-full justify-start">
                <ExternalLink className="mr-2 h-4 w-4" />
                View Live
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
