'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Plus,
  Search,
  Eye,
  Calendar,
  Edit,
  Trash2,
  Filter
} from "lucide-react"

const mockPosts = [
  {
    id: 1,
    title: "501+ Best Good Night Shayari in Hindi – सुकून भरी शुभ रात्रि",
    slug: "/post-4629",
    category: "Good Night",
    status: "Published",
    views: 1234,
    publishedAt: "2025-05-21",
  },
  {
    id: 2,
    title: "501+ Best Pyar Bhari <PERSON> – दिल से जुड़ी मोहब्बत",
    slug: "/post-7165",
    category: "Love Shayari",
    status: "Published",
    views: 987,
    publishedAt: "2025-05-16",
  },
  {
    id: 3,
    title: "301+ Best Miss You <PERSON>ad <PERSON> – अधूरी यादों की सच्ची आवाज़",
    slug: "/post-7468",
    category: "Sad Shayari",
    status: "Draft",
    views: 0,
    publishedAt: "Not published",
  },
  {
    id: 4,
    title: "100+ Best Motivational Thoughts In Hindi – जरूर पढ़ें! ✅",
    slug: "/motivational-thoughts-hindi",
    category: "Motivational",
    status: "Published",
    views: 2341,
    publishedAt: "2025-02-20",
  },
]

export default function PostsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  const filteredPosts = mockPosts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || post.status.toLowerCase() === statusFilter.toLowerCase()
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Posts</h1>
          <p className="text-muted-foreground">Manage your blog posts and content</p>
        </div>
        <Link href="/admin/posts/new">
          <button className="btn btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            New Post
          </button>
        </Link>
      </div>

      {/* Search and Filter */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Search & Filter Posts</h3>
          <p className="card-description">Find posts by title, category, or filter by status</p>
        </div>
        <div className="card-content">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder="Search posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="select w-48"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
          </div>
        </div>
      </div>

      {/* Posts Table */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">All Posts ({filteredPosts.length})</h3>
          <p className="card-description">Manage your blog posts, SEO settings, and publication status</p>
        </div>
        <div className="card-content">
          <table className="table">
            <thead className="table-header">
              <tr className="table-row">
                <th className="table-head">Title</th>
                <th className="table-head">Category</th>
                <th className="table-head">Status</th>
                <th className="table-head">Views</th>
                <th className="table-head">Published</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredPosts.map((post) => (
                <tr key={post.id} className="table-row">
                  <td className="table-cell">
                    <div>
                      <p className="font-medium text-foreground">{post.title}</p>
                      <p className="text-sm text-muted-foreground">{post.slug}</p>
                    </div>
                  </td>
                  <td className="table-cell">
                    <span className="badge badge-outline">{post.category}</span>
                  </td>
                  <td className="table-cell">
                    <span
                      className={`badge ${post.status === 'Published' ? 'badge-default' : 'badge-secondary'}`}
                    >
                      {post.status}
                    </span>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-1">
                      <Eye className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{post.views.toLocaleString()}</span>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-1">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{post.publishedAt}</span>
                    </div>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <Link href={`/admin/posts/${post.id}/edit`}>
                        <button className="btn btn-ghost btn-icon">
                          <Edit className="h-4 w-4" />
                        </button>
                      </Link>
                      <button className="btn btn-ghost btn-icon">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
