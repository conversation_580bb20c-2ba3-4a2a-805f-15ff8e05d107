'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  Plus,
  Search,
  Eye,
  Calendar,
  Edit,
  Trash2,
  Filter
} from "lucide-react"

interface Post {
  id: number
  title: string
  slug: string
  status: string
  view_count: number
  published_at: string | null
  created_at: string
}

export default function PostsPage() {
  const [posts, setPosts] = useState<Post[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  // Fetch posts from API
  useEffect(() => {
    const fetchPosts = async () => {
      try {
        const response = await fetch('/api/admin/posts')
        if (response.ok) {
          const data = await response.json()
          setPosts(data.posts || [])
        }
      } catch (error) {
        console.error('Error fetching posts:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchPosts()
  }, [])

  const filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || post.status.toLowerCase() === statusFilter.toLowerCase()
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Posts</h1>
          <p className="text-muted-foreground">Manage your blog posts and content</p>
        </div>
        <Link href="/admin/posts/new">
          <button className="btn btn-primary">
            <Plus className="mr-2 h-4 w-4" />
            New Post
          </button>
        </Link>
      </div>

      {/* Search and Filter */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">Search & Filter Posts</h3>
          <p className="card-description">Find posts by title, category, or filter by status</p>
        </div>
        <div className="card-content">
          <div className="flex gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <input
                type="text"
                placeholder="Search posts..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input pl-10"
              />
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="select w-48"
            >
              <option value="all">All Status</option>
              <option value="published">Published</option>
              <option value="draft">Draft</option>
            </select>
          </div>
        </div>
      </div>

      {/* Posts Table */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">All Posts ({loading ? '...' : filteredPosts.length})</h3>
          <p className="card-description">Manage your blog posts, SEO settings, and publication status</p>
        </div>
        <div className="card-content">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">Loading posts...</div>
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-muted-foreground">No posts found</div>
            </div>
          ) : (
            <table className="table">
              <thead className="table-header">
                <tr className="table-row">
                  <th className="table-head">Title</th>
                  <th className="table-head">Status</th>
                  <th className="table-head">Views</th>
                  <th className="table-head">Published</th>
                  <th className="table-head">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredPosts.map((post) => (
                  <tr key={post.id} className="table-row">
                    <td className="table-cell">
                      <div>
                        <p className="font-medium text-foreground">{post.title}</p>
                        <p className="text-sm text-muted-foreground">/{post.slug}</p>
                      </div>
                    </td>
                    <td className="table-cell">
                      <span
                        className={`badge ${post.status === 'published' ? 'badge-default' : 'badge-secondary'}`}
                      >
                        {post.status.charAt(0).toUpperCase() + post.status.slice(1)}
                      </span>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-1">
                        <Eye className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{post.view_count.toLocaleString()}</span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">
                          {post.published_at
                            ? new Date(post.published_at).toLocaleDateString()
                            : 'Not published'
                          }
                        </span>
                      </div>
                    </td>
                    <td className="table-cell">
                      <div className="flex items-center space-x-2">
                        <Link href={`/admin/posts/${post.id}/edit`}>
                          <button className="btn btn-ghost btn-icon">
                            <Edit className="h-4 w-4" />
                          </button>
                        </Link>
                        <button className="btn btn-ghost btn-icon text-destructive">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  )
}
