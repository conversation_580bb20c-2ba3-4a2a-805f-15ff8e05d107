'use client'

import { useState } from 'react'
import {
  Plus,
  Edit,
  Trash2,
  <PERSON><PERSON>er<PERSON><PERSON>,
  FileText
} from "lucide-react"

const mockCategories = [
  {
    id: 1,
    name: "Good Night Shayari",
    slug: "good-night",
    description: "Peaceful and soothing good night poetry",
    postCount: 8,
    color: "#3B82F6"
  },
  {
    id: 2,
    name: "<PERSON> Shayari",
    slug: "love",
    description: "Romantic poetry expressing love and affection",
    postCount: 12,
    color: "#EF4444"
  },
  {
    id: 3,
    name: "<PERSON> Shay<PERSON>",
    slug: "sad",
    description: "Emotional poetry about sadness and heartbreak",
    postCount: 6,
    color: "#6B7280"
  },
  {
    id: 4,
    name: "Motivational",
    slug: "motivational",
    description: "Inspiring thoughts and motivational content",
    postCount: 4,
    color: "#10B981"
  },
]

export default function CategoriesPage() {
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingCategory, setEditingCategory] = useState<any>(null)
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3B82F6'
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Saving category:', formData)
    setIsDialogOpen(false)
    setEditingCategory(null)
    setFormData({ name: '', slug: '', description: '', color: '#3B82F6' })
  }

  const handleEdit = (category: any) => {
    setEditingCategory(category)
    setFormData({
      name: category.name,
      slug: category.slug,
      description: category.description,
      color: category.color
    })
    setIsDialogOpen(true)
  }

  const handleDelete = (categoryId: number) => {
    console.log('Deleting category:', categoryId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Categories</h1>
          <p className="text-muted-foreground">Organize your blog posts with categories</p>
        </div>
        <button
          className="btn btn-primary"
          onClick={() => setIsDialogOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" />
          New Category
        </button>
      </div>

      {/* Modal */}
      {isDialogOpen && (
        <div className="modal-overlay" onClick={() => setIsDialogOpen(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2 className="modal-title">
                {editingCategory ? 'Edit Category' : 'Create New Category'}
              </h2>
              <p className="modal-description">
                {editingCategory
                  ? 'Update the category information below.'
                  : 'Add a new category to organize your blog posts.'
                }
              </p>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="label">Category Name</label>
                <input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="Enter category name"
                  required
                  className="input"
                />
              </div>
              <div>
                <label htmlFor="slug" className="label">URL Slug</label>
                <input
                  id="slug"
                  type="text"
                  value={formData.slug}
                  onChange={(e) => setFormData({...formData, slug: e.target.value})}
                  placeholder="category-url-slug"
                  required
                  className="input"
                />
              </div>
              <div>
                <label htmlFor="description" className="label">Description</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  placeholder="Brief description of the category"
                  rows={3}
                  className="input textarea"
                />
              </div>
              <div>
                <input
                  id="color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({...formData, color: e.target.value})}
                  className="w-20 h-10 input"
                />
              </div>
              <div className="flex justify-end space-x-2">
                <button
                  type="button"
                  className="btn btn-outline"
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingCategory ? 'Update' : 'Create'} Category
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Categories Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {mockCategories.map((category) => (
          <div key={category.id} className="card">
            <div className="card-header">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: category.color }}
                  />
                  <h3 className="card-title">{category.name}</h3>
                </div>
                <span className="badge badge-secondary">{category.postCount} posts</span>
              </div>
              <p className="card-description">{category.description}</p>
            </div>
            <div className="card-content">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <FolderOpen className="h-4 w-4" />
                  <span>/{category.slug}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <button
                    className="btn btn-ghost btn-icon"
                    onClick={() => handleEdit(category)}
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    className="btn btn-ghost btn-icon"
                    onClick={() => handleDelete(category.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Categories Table */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">All Categories</h3>
          <p className="card-description">Detailed view of all blog categories</p>
        </div>
        <div className="card-content">
          <table className="table">
            <thead className="table-header">
              <tr className="table-row">
                <th className="table-head">Name</th>
                <th className="table-head">Slug</th>
                <th className="table-head">Posts</th>
                <th className="table-head">Description</th>
                <th className="table-head">Actions</th>
              </tr>
            </thead>
            <tbody>
              {mockCategories.map((category) => (
                <tr key={category.id} className="table-row">
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: category.color }}
                      />
                      <span className="font-medium text-foreground">{category.name}</span>
                    </div>
                  </td>
                  <td className="table-cell">
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      /{category.slug}
                    </code>
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-1">
                      <FileText className="h-4 w-4 text-muted-foreground" />
                      <span>{category.postCount}</span>
                    </div>
                  </td>
                  <td className="table-cell max-w-xs truncate">
                    {category.description}
                  </td>
                  <td className="table-cell">
                    <div className="flex items-center space-x-2">
                      <button
                        className="btn btn-ghost btn-icon"
                        onClick={() => handleEdit(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        className="btn btn-ghost btn-icon"
                        onClick={() => handleDelete(category.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )
}
