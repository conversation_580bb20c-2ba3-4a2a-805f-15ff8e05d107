import Link from 'next/link';
import { Post } from '@/types/blog';

interface ShayariCardProps {
  post: Post;
}

export default function ShayariCard({ post }: ShayariCardProps) {
  const postDate = new Date(post.published_at).toLocaleDateString('hi-IN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <div className="shayari-card">
      <div className="card-image-placeholder"></div>
      <h3 className="card-title">
        <Link href={`/posts/${post.slug}`}>{post.title}</Link>
      </h3>
      {/* The wireframe had an author, but the Post type does not. This can be added later. */}
      {/* <p className="card-author">लेखक: {post.author.name}</p> */}
      <p className="card-excerpt">{post.excerpt}</p>
      <div className="card-meta">
        {/* The wireframe had a category, but the Post type does not. This can be added later. */}
        {/* <span className="card-category">{post.category}</span> */}
        <span className="card-date">{postDate}</span>
      </div>
      <div className="card-actions">
        <div className="card-engagement">
          <button className="like-count">
            ❤️ <span>{post.view_count}</span>
          </button>
          <button className="share-btn">
            📤 Share
          </button>
        </div>
        <Link href={`/posts/${post.slug}`} className="btn btn--sm btn--primary read-full-btn">
          पढ़ें
        </Link>
      </div>
    </div>
  );
}
