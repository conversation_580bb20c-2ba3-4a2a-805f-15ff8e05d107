'use client'

import { useAuth } from '@/contexts/AuthContext'
import { usePathname } from 'next/navigation'

interface AdminAuthGuardProps {
  children: React.ReactNode
}

export function AdminAuthGuard({ children }: AdminAuthGuardProps) {
  const { isAuthenticated } = useAuth()
  const pathname = usePathname()

  // Allow access to login page without authentication
  if (pathname === '/admin/login') {
    return <>{children}</>
  }

  // Require authentication for all other admin routes
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return <>{children}</>
}
