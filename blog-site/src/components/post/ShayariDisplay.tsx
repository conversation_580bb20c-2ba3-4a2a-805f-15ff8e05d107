'use client';

import { Post, User } from '@/types/blog';
import { useState } from 'react';

interface ShayariDisplayProps {
  post: Post;
  author: User | null;
}

export function ShayariDisplay({ post, author }: ShayariDisplayProps) {
  const [likes, setLikes] = useState(0);

  const handleLike = () => {
    setLikes(likes + 1);
  };

  const handleCopy = () => {
    const textToCopy = `${post.content}\n\n- ${author?.display_name || 'Anonymous'}`;
    navigator.clipboard.writeText(textToCopy).then(() => {
      alert('शायरी कॉपी हो गई!');
    }).catch(err => {
      console.error('Failed to copy text: ', err);
    });
  };

  return (
    <div className="shayari-collection">
      <div className="shayari-card" data-theme="love">
        <div className="shayari-background love-bg">
          <div className="shayari-overlay">
            <div className="shayari-content">
              <div className="shayari-text" dangerouslySetInnerHTML={{ __html: post.content.replace(/\n/g, '<p class="shayari-line">') }}>
              </div>
              <div className="shayari-author">- {author?.display_name || 'Anonymous'}</div>
            </div>
          </div>
        </div>
        <div className="shayari-actions">
          <button className="action-btn like-btn" onClick={handleLike}>
            <span className="btn-icon">❤️</span>
            <span className="btn-text">{likes}</span>
          </button>
          <div className="share-dropdown">
            <button className="action-btn share-btn">
              <span className="btn-icon">📤</span>
              <span className="btn-text">शेयर</span>
            </button>
            <div className="share-menu hidden"></div>
          </div>
          <button className="action-btn copy-btn" onClick={handleCopy}>
            <span className="btn-icon">📋</span>
            <span className="btn-text">कॉपी</span>
          </button>
          <button className="action-btn download-btn">
            <span className="btn-icon">📥</span>
            <span className="btn-text">डाउनलोड</span>
          </button>
        </div>
      </div>
    </div>
  );
}
