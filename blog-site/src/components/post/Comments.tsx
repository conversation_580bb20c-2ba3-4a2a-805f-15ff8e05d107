'use client';

import { useState } from 'react';

interface Comment {
  id: number;
  text: string;
  author: string;
}

export function Comments() {
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      setComments([...comments, { id: Date.now(), text: newComment, author: 'You' }]);
      setNewComment('');
    }
  };

  return (
    <section className="comments-section">
      <h3 className="comments-title">टिप्पणियाँ ({comments.length})</h3>
      <form className="comment-form" onSubmit={handleSubmit}>
        <textarea
          className="form-control comment-input"
          placeholder="अपनी टिप्पणी लिखें..."
          rows={3}
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
        ></textarea>
        <button type="submit" className="btn btn--primary">टिप्पणी पोस्ट करें</button>
      </form>
      <div className="comments-list">
        {comments.map(comment => (
          <div key={comment.id} className="comment">
            <div className="comment-avatar"></div>
            <div className="comment-content">
              <div className="comment-header">
                <h4 className="comment-author">{comment.author}</h4>
              </div>
              <p className="comment-text">{comment.text}</p>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}
