'use client'

import { AdminHeader } from "./AdminHeader"
import { AdminSidebar } from "./AdminSidebar"
import { AuthProvider } from "@/contexts/AuthContext"
import { AdminAuthGuard } from "./AdminAuthGuard"

export function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <AdminAuthGuard>
        <div className="admin-container">
          <AdminHeader />
          <div className="admin-body">
            <AdminSidebar />
            <main className="admin-main p-6">
              {children}
            </main>
          </div>
        </div>
      </AdminAuthGuard>
    </AuthProvider>
  )
}
