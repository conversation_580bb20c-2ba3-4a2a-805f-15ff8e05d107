'use client'

import { useState } from "react"
import { <PERSON>, Bell, Setting<PERSON>, User, LogOut } from "lucide-react"
import { useAuth } from "@/contexts/AuthContext"
import { useRouter } from "next/navigation"

export function AdminHeader() {
  const { logout } = useAuth()
  const router = useRouter()
  const [showUserMenu, setShowUserMenu] = useState(false)

  const handleLogout = async () => {
    await logout()
    router.push('/admin/login')
  }

  return (
    <header className="admin-header h-16 px-6 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-2">
          <h1 className="text-xl font-semibold text-foreground">Admin Panel</h1>
          <span className="badge badge-secondary text-xs">v1.0</span>
        </div>
      </div>

      <div className="flex items-center space-x-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <input
            type="text"
            placeholder="Search posts, categories..."
            className="input pl-10 w-64"
          />
        </div>

        {/* Notifications */}
        <button className="btn btn-ghost btn-icon relative">
          <Bell className="h-5 w-5" />
          <span className="badge badge-default absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs">
            3
          </span>
        </button>

        {/* Settings */}
        <button className="btn btn-ghost btn-icon">
          <Settings className="h-5 w-5" />
        </button>

        {/* User Menu */}
        <div className="relative">
          <button
            className="btn btn-ghost flex items-center space-x-2"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <User className="h-5 w-5" />
            <div className="text-left">
              <div className="text-sm font-medium">Admin User</div>
              <div className="text-xs text-muted-foreground"><EMAIL></div>
            </div>
          </button>

          {showUserMenu && (
            <div className="modal-content absolute right-0 top-full mt-2 w-56 z-50">
              <div className="modal-header">
                <p className="font-medium text-foreground">My Account</p>
              </div>
              <div className="space-y-1">
                <button className="w-full text-left px-3 py-2 text-sm hover:bg-accent flex items-center rounded">
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </button>
                <button className="w-full text-left px-3 py-2 text-sm hover:bg-accent flex items-center rounded">
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </button>
                <div className="border-t border-border my-1"></div>
                <button
                  className="w-full text-left px-3 py-2 text-sm hover:bg-accent flex items-center rounded"
                  onClick={handleLogout}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  Logout
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </header>
  )
}
