/* Admin-specific styles to override main website layout */

/* Hide main website header and footer on admin pages */
body:has([data-admin-page]) .header,
body:has([data-admin-page]) .footer {
  display: none !important;
}

/* Ensure admin layout takes full height */
body:has([data-admin-page]) {
  margin: 0;
  padding: 0;
}

/* Admin page container */
[data-admin-page] {
  min-height: 100vh;
  background: hsl(var(--background));
}

/* Reset any inherited styles for admin content */
[data-admin-page] * {
  box-sizing: border-box;
}

/* Admin-specific component styles */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background: hsl(var(--background));
}

.admin-sidebar {
  width: 250px;
  background: hsl(var(--card));
  border-right: 1px solid hsl(var(--border));
  flex-shrink: 0;
}

.admin-main {
  flex: 1;
  padding: 1.5rem;
  overflow-x: auto;
}

.admin-header {
  background: hsl(var(--card));
  border-bottom: 1px solid hsl(var(--border));
  padding: 1rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: between;
}

/* Override any conflicting styles from main site */
[data-admin-page] .container {
  max-width: none;
  padding: 0;
  margin: 0;
}

[data-admin-page] .nav-menu,
[data-admin-page] .nav-list,
[data-admin-page] .nav-link {
  all: unset;
}

/* Ensure admin forms and inputs work properly */
[data-admin-page] input,
[data-admin-page] textarea,
[data-admin-page] select,
[data-admin-page] button {
  font-family: inherit;
}
